"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import { Search, Plus, Edit, Trash2, Building2, MapPin, Phone, Mail, User, AlertTriangle, Loader2 } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { KumiaiService, type Kumiai } from "@/lib/services"

interface FormData {
  nama_kumiai: string
  kode_kumiai: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  keterangan?: string
}

interface FormErrors {
  nama_kumiai?: string
  kode_kumiai?: string
  alamat_jepang?: string
  kota_jepang?: string
  prefektur?: string
  kontak_person?: string
  nomor_telepon?: string
  email?: string
}

export default function KumiaiPage() {
  const [kumiaiData, setKumiaiData] = useState<Kumiai[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingKumiai, setDeletingKumiai] = useState<Kumiai | null>(null)

  // Fetch Kumiai data
  useEffect(() => {
    const fetchKumiaiData = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await KumiaiService.getWithCompanyCount()
        setKumiaiData(data)
      } catch (err) {
        console.error('Error fetching Kumiai data:', err)
        setError('Gagal memuat data Kumiai. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchKumiaiData()
  }, [])

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return kumiaiData
    
    return kumiaiData.filter(
      (kumiai) =>
        kumiai.nama_kumiai.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kumiai.kode_kumiai.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kumiai.prefektur.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kumiai.kontak_person.toLowerCase().includes(searchTerm.toLowerCase()),
    )
  }, [kumiaiData, searchTerm])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalKumiai = kumiaiData.length
    const totalPerusahaan = kumiaiData.reduce((sum, kumiai) => {
      const companyCount = (kumiai as any).perusahaan_penerima?.length || 0
      return sum + companyCount
    }, 0)
    const rataRataPerusahaan = totalKumiai > 0 ? Math.round(totalPerusahaan / totalKumiai) : 0
    const kumiaiAktif = kumiaiData.filter((kumiai) => kumiai.status === "aktif").length

    return {
      totalKumiai,
      totalPerusahaan,
      rataRataPerusahaan,
      kumiaiAktif,
    }
  }, [kumiaiData])

  // Handle delete
  const handleDelete = async () => {
    if (!deletingKumiai) return

    try {
      await KumiaiService.delete(deletingKumiai.id)
      setKumiaiData((prev) => prev.filter((kumiai) => kumiai.id !== deletingKumiai.id))
      toast({
        title: "Berhasil",
        description: `Kumiai ${deletingKumiai.nama_kumiai} berhasil dihapus`,
      })
      setIsDeleteDialogOpen(false)
      setDeletingKumiai(null)
    } catch (error) {
      console.error('Error deleting Kumiai:', error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menghapus data",
        variant: "destructive",
      })
    }
  }

  // Handle delete dialog
  const handleOpenDeleteDialog = (kumiai: Kumiai) => {
    setDeletingKumiai(kumiai)
    setIsDeleteDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data Kumiai...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Master Kumiai</h1>
          <p className="text-gray-600 mt-1">Kelola data koperasi pekerja (kumiai) di Jepang</p>
        </div>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah Kumiai
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Kumiai</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalKumiai}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Koperasi terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Kumiai Aktif</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.kumiaiAktif}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Sedang beroperasi</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Perusahaan</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalPerusahaan}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Dari semua Kumiai</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Rata-rata Perusahaan</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.rataRataPerusahaan}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Per Kumiai</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Table */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar Kumiai</CardTitle>
              <CardDescription>Kelola data koperasi pekerja di Jepang</CardDescription>
            </div>
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cari berdasarkan nama atau kode kumiai..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Nama Kumiai</TableHead>
                  <TableHead className="font-semibold">Kode</TableHead>
                  <TableHead className="font-semibold">Lokasi</TableHead>
                  <TableHead className="font-semibold">Kontak</TableHead>
                  <TableHead className="font-semibold text-center">Perusahaan</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((kumiai, index) => (
                    <TableRow key={kumiai.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{kumiai.nama_kumiai}</div>
                          {kumiai.keterangan && (
                            <div className="text-xs text-gray-500 mt-1 max-w-xs truncate">{kumiai.keterangan}</div>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <Badge
                              variant="outline"
                              className={
                                kumiai.status === "aktif"
                                  ? "bg-green-100 text-green-800 border-green-200"
                                  : "bg-gray-100 text-gray-800 border-gray-200"
                              }
                            >
                              {kumiai.status === "aktif" ? "Aktif" : "Nonaktif"}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-mono text-xs">
                          {kumiai.kode_kumiai}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{kumiai.kota_jepang}</span>
                          </div>
                          <div className="text-xs text-gray-500">{kumiai.prefektur}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{kumiai.kontak_person}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{kumiai.nomor_telepon}</span>
                          </div>
                          {kumiai.email && (
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span className="text-sm text-gray-700">{kumiai.email}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant="outline" className="text-xs">
                          {(kumiai as any).perusahaan_penerima?.length || 0} perusahaan
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-blue-50 hover:border-blue-300"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(kumiai)}
                            className="hover:bg-red-50 hover:border-red-300 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Building2 className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada data Kumiai</h3>
                        <p className="text-gray-500">
                          {searchTerm
                            ? "Tidak ditemukan Kumiai yang sesuai dengan pencarian"
                            : "Belum ada Kumiai yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {kumiaiData.length} Kumiai
                </span>
                {searchTerm && <span className="text-blue-600">Hasil pencarian untuk "{searchTerm}"</span>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus Kumiai{" "}
              <span className="font-semibold text-gray-900">"{deletingKumiai?.nama_kumiai}"</span>?
              <br />
              <br />
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data yang terkait dengan Kumiai ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
