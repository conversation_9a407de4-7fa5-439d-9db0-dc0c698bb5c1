"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import { Search, Plus, Eye, Edit, Trash2, Briefcase, Building2, Users, Calendar, AlertTriangle, Loader2 } from "lucide-react"
import Link from "next/link"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { JobOrderService, type JobOrder } from "@/lib/services"

export default function JobOrderPage() {
  const [jobOrderData, setJobOrderData] = useState<JobOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingJobOrder, setDeletingJobOrder] = useState<JobOrder | null>(null)

  // Fetch Job Order data
  useEffect(() => {
    const fetchJobOrderData = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await JobOrderService.getWithPlacementStats()
        setJobOrderData(data)
      } catch (err) {
        console.error('Error fetching Job Order data:', err)
        setError('Gagal memuat data Job Order. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchJobOrderData()
  }, [])

  // Filter data based on search term and status
  const filteredData = useMemo(() => {
    let filtered = jobOrderData

    if (searchTerm) {
      filtered = filtered.filter(
        (job) =>
          job.judul_pekerjaan.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.posisi.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.bidang_kerja.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (job as any).perusahaan_penerima?.nama_perusahaan.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter && statusFilter !== "all") {
      filtered = filtered.filter((job) => job.status === statusFilter)
    }

    return filtered
  }, [jobOrderData, searchTerm, statusFilter])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalJobOrder = jobOrderData.length
    const published = jobOrderData.filter((job) => job.status === "published").length
    const closed = jobOrderData.filter((job) => job.status === "closed").length
    const draft = jobOrderData.filter((job) => job.status === "draft").length
    const totalKuota = jobOrderData.reduce((sum, job) => sum + job.jumlah_kuota, 0)
    const kuotaTerisi = jobOrderData.reduce((sum, job) => sum + job.kuota_terisi, 0)

    return {
      totalJobOrder,
      published,
      closed,
      draft,
      totalKuota,
      kuotaTerisi,
      kuotaTersedia: totalKuota - kuotaTerisi,
    }
  }, [jobOrderData])

  // Handle delete
  const handleDelete = async () => {
    if (!deletingJobOrder) return

    try {
      await JobOrderService.delete(deletingJobOrder.id)
      setJobOrderData((prev) => prev.filter((job) => job.id !== deletingJobOrder.id))
      toast({
        title: "Berhasil",
        description: `Job Order ${deletingJobOrder.judul_pekerjaan} berhasil dihapus`,
      })
      setIsDeleteDialogOpen(false)
      setDeletingJobOrder(null)
    } catch (error) {
      console.error('Error deleting Job Order:', error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menghapus data",
        variant: "destructive",
      })
    }
  }

  // Handle delete dialog
  const handleOpenDeleteDialog = (jobOrder: JobOrder) => {
    setDeletingJobOrder(jobOrder)
    setIsDeleteDialogOpen(true)
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Terbuka</Badge>
      case 'closed':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Ditutup</Badge>
      case 'draft':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Draft</Badge>
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Dibatalkan</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data Job Order...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Job Order Management</h1>
          <p className="text-gray-600 mt-1">Kelola lowongan pekerjaan untuk program magang Jepang</p>
        </div>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah Job Order
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Job Order</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Briefcase className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalJobOrder}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Lowongan terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Job Order Aktif</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <Briefcase className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.published}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Sedang terbuka</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Kuota</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalKuota}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Posisi tersedia</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Kuota Terisi</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.kuotaTerisi}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Sudah ditempatkan</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar Job Order</CardTitle>
              <CardDescription>Kelola lowongan pekerjaan untuk program magang</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <div className="relative w-full sm:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari berdasarkan judul atau perusahaan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="all">Semua Status</option>
                <option value="published">Terbuka</option>
                <option value="closed">Ditutup</option>
                <option value="draft">Draft</option>
                <option value="cancelled">Dibatalkan</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Job Order</TableHead>
                  <TableHead className="font-semibold">Perusahaan</TableHead>
                  <TableHead className="font-semibold">Kuota</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Tanggal</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((job, index) => (
                    <TableRow key={job.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{job.judul_pekerjaan}</div>
                          <div className="text-sm text-gray-500">{job.posisi}</div>
                          <div className="text-xs text-gray-400 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {job.bidang_kerja}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-700">
                            {(job as any).perusahaan_penerima?.nama_perusahaan || 'N/A'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {(job as any).kumiai?.nama_kumiai || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{job.kuota_terisi}/{job.jumlah_kuota}</div>
                          <div className="text-xs text-gray-500">
                            {job.jumlah_kuota - job.kuota_terisi} tersisa
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(job.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-gray-400" />
                            <span>{new Date(job.tanggal_buka).toLocaleDateString('id-ID')}</span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            s/d {new Date(job.tanggal_tutup).toLocaleDateString('id-ID')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Link href={`/job-order/${job.id}`}>
                            <Button
                              variant="outline"
                              size="sm"
                              className="hover:bg-blue-50 hover:border-blue-300"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-green-50 hover:border-green-300"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(job)}
                            className="hover:bg-red-50 hover:border-red-300 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Briefcase className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada Job Order</h3>
                        <p className="text-gray-500">
                          {searchTerm || statusFilter
                            ? "Tidak ditemukan Job Order yang sesuai dengan filter"
                            : "Belum ada Job Order yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {jobOrderData.length} Job Order
                </span>
                {(searchTerm || statusFilter) && (
                  <span className="text-blue-600">
                    Filter aktif: {searchTerm && `"${searchTerm}"`} {statusFilter && `Status: ${statusFilter}`}
                  </span>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus Job Order{" "}
              <span className="font-semibold text-gray-900">"{deletingJobOrder?.judul_pekerjaan}"</span>?
              <br />
              <br />
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data yang terkait dengan Job Order ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
