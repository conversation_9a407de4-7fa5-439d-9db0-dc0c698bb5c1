"use client"

import { useState } from "react"
import {
  ArrowLeft,
  Edit,
  Building2,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Briefcase,
  FileText,
  Eye,
  TrendingUp,
  Award,
  Globe,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

// Sample job order data
const jobOrderData = {
  id: "JO-2024-001",
  namaPerusahaan: "Toyota Motor Corporation",
  posisi: "Assembly Line Operator",
  jumlahKuota: 25,
  syaratKhusus: [
    "Minimal Bahasa Jepang N4",
    "Pengalaman kerja di bidang manufaktur minimal 1 tahun",
    "Usia maksimal 28 tahun",
    "Tinggi badan minimal 160 cm",
    "Tidak buta warna",
    "Sehat jasmani dan rohani",
  ],
  tanggalInput: "2024-01-10",
  statusJobOrder: "Terbuka",
  deskripsi:
    "Posisi ini membutuhkan tenaga kerja yang dapat bekerja dalam tim untuk proses assembly kendaraan. Kandidat akan ditempatkan di pabrik Toyota di Aichi Prefecture dengan fasilitas lengkap termasuk asrama dan asuransi kesehatan.",
  lokasiKerja: "Aichi Prefecture, Jepang",
  gajiPerBulan: "¥180,000 - ¥220,000",
  kontrakKerja: "3 Tahun",
  fasilitasLain: ["Asrama", "Asuransi Kesehatan", "Pelatihan Bahasa Jepang", "Transportasi"],
}

// Sample statistics data
const statisticsData = [
  {
    title: "Total Pendaftar",
    value: 45,
    icon: Users,
    color: "bg-blue-500",
    description: "Siswa yang mendaftar",
  },
  {
    title: "Lolos Administrasi",
    value: 32,
    icon: FileText,
    color: "bg-green-500",
    description: "Dari 45 pendaftar",
  },
  {
    title: "Lolos Wawancara",
    value: 28,
    icon: CheckCircle,
    color: "bg-orange-500",
    description: "Dari 32 yang lolos administrasi",
  },
  {
    title: "Siap Berangkat",
    value: 25,
    icon: TrendingUp,
    color: "bg-maroon-600",
    description: "Sesuai kuota yang dibutuhkan",
  },
]

// Sample students data
const studentsData = [
  {
    id: 1,
    nama: "Ahmad Rizki Pratama",
    lpkMitra: "LPK Sukses Mandiri Jakarta",
    statusSeleksi: "Siap Berangkat",
    tanggalDaftar: "2024-01-15",
    nilaiWawancara: 85,
  },
  {
    id: 2,
    nama: "Siti Nurhaliza",
    lpkMitra: "LPK Maju Bersama Bandung",
    statusSeleksi: "Pemberkasan",
    tanggalDaftar: "2024-01-14",
    nilaiWawancara: 78,
  },
  {
    id: 3,
    nama: "Budi Santoso",
    lpkMitra: "LPK Harapan Bangsa Surabaya",
    statusSeleksi: "Lolos Wawancara",
    tanggalDaftar: "2024-01-13",
    nilaiWawancara: 82,
  },
  {
    id: 4,
    nama: "Dewi Sartika",
    lpkMitra: "LPK Karya Utama Medan",
    statusSeleksi: "Lolos Administrasi",
    tanggalDaftar: "2024-01-12",
    nilaiWawancara: null,
  },
  {
    id: 5,
    nama: "Andi Wijaya",
    lpkMitra: "LPK Nusantara Makassar",
    statusSeleksi: "Siap Berangkat",
    tanggalDaftar: "2024-01-11",
    nilaiWawancara: 88,
  },
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    "Lolos Administrasi": { color: "bg-blue-100 text-blue-800 border-blue-200", dot: "bg-blue-500" },
    "Lolos Wawancara": { color: "bg-purple-100 text-purple-800 border-purple-200", dot: "bg-purple-500" },
    Pemberkasan: { color: "bg-orange-100 text-orange-800 border-orange-200", dot: "bg-orange-500" },
    "Siap Berangkat": { color: "bg-green-100 text-green-800 border-green-200", dot: "bg-green-500" },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Lolos Administrasi"]

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
      {status}
    </Badge>
  )
}

const getJobOrderStatusBadge = (status: string) => {
  return status === "Terbuka" ? (
    <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-100">
      <CheckCircle className="w-3 h-3 mr-1" />
      Terbuka
    </Badge>
  ) : (
    <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-100">
      <Clock className="w-3 h-3 mr-1" />
      Ditutup
    </Badge>
  )
}

export default function DetailJobOrder() {
  const [activeTab, setActiveTab] = useState("overview")

  const progressPercentage = Math.round((statisticsData[3].value / jobOrderData.jumlahKuota) * 100)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <Briefcase className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Detail Job Order</h1>
                  <p className="text-gray-600 mt-1">ID: {jobOrderData.id}</p>
                </div>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
              <Edit className="h-4 w-4 mr-2" />
              Edit Job Order
            </Button>
          </div>
        </div>

        {/* Job Order Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Main Information */}
          <Card className="lg:col-span-2">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl text-gray-800 flex items-center">
                    <Building2 className="h-5 w-5 mr-2 text-orange-600" />
                    {jobOrderData.namaPerusahaan}
                  </CardTitle>
                  <CardDescription className="text-lg font-medium text-gray-700 mt-1">
                    {jobOrderData.posisi}
                  </CardDescription>
                </div>
                {getJobOrderStatusBadge(jobOrderData.statusJobOrder)}
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Jumlah Kuota</p>
                      <p className="font-semibold text-lg">{jobOrderData.jumlahKuota} Orang</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Tanggal Input</p>
                      <p className="font-semibold">
                        {new Date(jobOrderData.tanggalInput).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "long",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Lokasi Kerja</p>
                      <p className="font-semibold">{jobOrderData.lokasiKerja}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Globe className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Gaji per Bulan</p>
                      <p className="font-semibold">{jobOrderData.gajiPerBulan}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Kontrak Kerja</p>
                      <p className="font-semibold">{jobOrderData.kontrakKerja}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Award className="h-5 w-5 text-orange-600 mr-3 mt-1" />
                    <div>
                      <p className="text-sm text-gray-500">Fasilitas</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {jobOrderData.fasilitasLain.map((fasilitas, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {fasilitas}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-orange-600" />
                  Syarat Khusus
                </h3>
                <ul className="space-y-2">
                  {jobOrderData.syaratKhusus.map((syarat, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{syarat}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {jobOrderData.deskripsi && (
                <>
                  <Separator className="my-6" />
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">Deskripsi Tambahan</h3>
                    <p className="text-sm text-gray-700 leading-relaxed">{jobOrderData.deskripsi}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Progress Card */}
          <Card>
            <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
              <CardTitle className="text-lg text-gray-800 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                Progress Rekrutmen
              </CardTitle>
              <CardDescription>Status pemenuhan kuota job order</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {statisticsData[3].value}/{jobOrderData.jumlahKuota}
                </div>
                <p className="text-sm text-gray-500 mb-4">Siswa siap diberangkatkan</p>
                <Progress value={progressPercentage} className="h-3" />
                <p className="text-xs text-gray-500 mt-2">{progressPercentage}% dari target</p>
              </div>

              <div className="space-y-4">
                {statisticsData.map((stat, index) => {
                  const IconComponent = stat.icon
                  return (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className={`${stat.color} p-2 rounded-lg mr-3`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800">{stat.title}</p>
                          <p className="text-xs text-gray-500">{stat.description}</p>
                        </div>
                      </div>
                      <div className="text-lg font-bold text-gray-900">{stat.value}</div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Daftar Siswa Terdaftar</CardTitle>
                <CardDescription>Siswa yang mendaftar untuk job order ini</CardDescription>
              </div>
              <Badge variant="outline" className="text-sm">
                {studentsData.length} Siswa
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Nama Siswa</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">LPK Mitra</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Status Seleksi</TableHead>
                    <TableHead className="text-center font-semibold">Nilai Wawancara</TableHead>
                    <TableHead className="font-semibold">Tanggal Daftar</TableHead>
                    <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentsData.map((student, index) => (
                    <TableRow key={student.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell className="font-medium">{student.nama}</TableCell>
                      <TableCell className="text-sm">{student.lpkMitra}</TableCell>
                      <TableCell>{getStatusBadge(student.statusSeleksi)}</TableCell>
                      <TableCell className="text-center">
                        {student.nilaiWawancara ? (
                          <Badge
                            variant="outline"
                            className={
                              student.nilaiWawancara >= 80
                                ? "bg-green-100 text-green-800 border-green-200"
                                : student.nilaiWawancara >= 70
                                  ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                                  : "bg-red-100 text-red-800 border-red-200"
                            }
                          >
                            {student.nilaiWawancara}
                          </Badge>
                        ) : (
                          <span className="text-gray-400 text-sm">Belum</span>
                        )}
                      </TableCell>
                      <TableCell className="text-sm">
                        {new Date(student.tanggalDaftar).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </TableCell>
                      <TableCell className="text-center">
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {studentsData.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada siswa terdaftar</h3>
                <p className="text-gray-500">Siswa akan muncul di sini setelah mendaftar untuk job order ini.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
