"use client"
import {
  ArrowLeft,
  Edit,
  User,
  MapPin,
  Building2,
  BadgeIcon as IdCard,
  CheckCircle,
  Clock,
  Circle,
  Calendar,
  FileText,
  MessageSquare,
  GraduationCap,
  Award,
  Plane,
  BookOpen,
  ClipboardCheck,
  FileCheck,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Sample student data
const studentData = {
  id: 1,
  namaLengkap: "<PERSON>",
  gender: "La<PERSON>-laki",
  nik: "3201234567890123",
  lpkMitra: "LPK Sukses Mandiri Jakarta",
  kotaKabupaten: "Jakarta Selatan",
  tanggalLahir: "1995-05-15",
  alamat: "Jl. Merdeka No. 123, Kebayoran Baru, Jakarta Selatan",
  noTelepon: "081234567890",
  email: "<EMAIL>",
  tanggalDaftar: "2024-01-15",
  jobOrder: "Toyota Motor Corporation - Assembly Line Operator",
}

// Timeline stages with status
const timelineStages = [
  {
    id: 1,
    title: "Pre-seleksi",
    description: "Verifikasi dokumen awal dan kelengkapan berkas",
    icon: ClipboardCheck,
    status: "selesai",
    tanggal: "2024-01-20",
    catatan: "Dokumen lengkap dan memenuhi syarat dasar",
  },
  {
    id: 2,
    title: "Pendidikan Pra-Diklat",
    description: "Persiapan dasar sebelum memasuki program diklat",
    icon: BookOpen,
    status: "selesai",
    tanggal: "2024-02-01",
    catatan: "Lulus dengan nilai baik, siap melanjutkan ke tahap diklat",
  },
  {
    id: 3,
    title: "Pendidikan Diklat",
    description: "Program pelatihan teknis dan bahasa Jepang",
    icon: GraduationCap,
    status: "selesai",
    tanggal: "2024-03-15",
    catatan: "Menyelesaikan 320 jam pelatihan dengan nilai A",
  },
  {
    id: 4,
    title: "Seleksi Administrasi",
    description: "Verifikasi kelengkapan dokumen untuk seleksi",
    icon: FileCheck,
    status: "selesai",
    tanggal: "2024-03-20",
    catatan: "Semua dokumen telah diverifikasi dan dinyatakan lengkap",
  },
  {
    id: 5,
    title: "Wawancara",
    description: "Tes wawancara dengan pihak perusahaan Jepang",
    icon: MessageSquare,
    status: "berlangsung",
    tanggal: "2024-03-25",
    catatan: "Dijadwalkan wawancara online dengan HR Toyota",
  },
  {
    id: 6,
    title: "Pemberkasan",
    description: "Pengurusan dokumen resmi untuk keberangkatan",
    icon: FileText,
    status: "belum_mulai",
    tanggal: null,
    catatan: null,
  },
  {
    id: 7,
    title: "Pendidikan Pasca-Diklat",
    description: "Pelatihan lanjutan dan orientasi budaya Jepang",
    icon: Award,
    status: "belum_mulai",
    tanggal: null,
    catatan: null,
  },
  {
    id: 8,
    title: "Surat Rekomendasi Disnaker",
    description: "Penerbitan surat rekomendasi dari Dinas Tenaga Kerja",
    icon: Award,
    status: "belum_mulai",
    tanggal: null,
    catatan: null,
  },
  {
    id: 9,
    title: "Pemberangkatan ke Jepang",
    description: "Keberangkatan menuju tempat kerja di Jepang",
    icon: Plane,
    status: "belum_mulai",
    tanggal: null,
    catatan: null,
  },
]

const getStatusConfig = (status: string) => {
  switch (status) {
    case "selesai":
      return {
        color: "bg-maroon-600",
        textColor: "text-maroon-600",
        bgColor: "bg-maroon-50",
        borderColor: "border-maroon-200",
        icon: CheckCircle,
        badge: "bg-maroon-100 text-maroon-800 border-maroon-200",
        label: "Selesai",
      }
    case "berlangsung":
      return {
        color: "bg-orange-500",
        textColor: "text-orange-500",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
        icon: Clock,
        badge: "bg-orange-100 text-orange-800 border-orange-200",
        label: "Berlangsung",
      }
    default:
      return {
        color: "bg-gray-400",
        textColor: "text-gray-400",
        bgColor: "bg-gray-50",
        borderColor: "border-gray-200",
        icon: Circle,
        badge: "bg-gray-100 text-gray-600 border-gray-200",
        label: "Belum Mulai",
      }
  }
}

export default function DetailSiswa() {
  const currentStage = timelineStages.find((stage) => stage.status === "berlangsung")
  const completedStages = timelineStages.filter((stage) => stage.status === "selesai").length
  const totalStages = timelineStages.length
  const progressPercentage = Math.round((completedStages / totalStages) * 100)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <a href="/siswa" className="text-gray-500 hover:text-gray-700">
                  Data Siswa
                </a>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Detail Siswa</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => (window.location.href = "/siswa")}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <User className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Detail & Timeline Progress Siswa</h1>
                  <p className="text-gray-600 mt-1">Informasi lengkap dan tracking progress seleksi</p>
                </div>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
              <Edit className="h-4 w-4 mr-2" />
              Edit Data Siswa
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Student Information */}
          <div className="lg:col-span-1 space-y-6">
            {/* Basic Info Card */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
                <CardTitle className="text-lg text-gray-800 flex items-center">
                  <User className="h-5 w-5 mr-2 text-orange-600" />
                  Informasi Siswa
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                    <p className="text-lg font-semibold text-gray-900">{studentData.namaLengkap}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Gender</label>
                      <p className="font-medium text-gray-900">{studentData.gender}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tanggal Lahir</label>
                      <p className="font-medium text-gray-900">
                        {new Date(studentData.tanggalLahir).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "long",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center">
                      <IdCard className="h-4 w-4 mr-1" />
                      NIK
                    </label>
                    <p className="font-mono text-gray-900">{studentData.nik}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      Kota/Kabupaten
                    </label>
                    <p className="font-medium text-gray-900">{studentData.kotaKabupaten}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center">
                      <Building2 className="h-4 w-4 mr-1" />
                      LPK Mitra
                    </label>
                    <p className="font-medium text-gray-900">{studentData.lpkMitra}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Progress Summary */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
                <CardTitle className="text-lg text-gray-800">Ringkasan Progress</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {completedStages}/{totalStages}
                  </div>
                  <p className="text-sm text-gray-500 mb-4">Tahapan selesai</p>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-maroon-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">{progressPercentage}% selesai</p>
                </div>

                {currentStage && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Clock className="h-4 w-4 text-orange-600 mr-2" />
                      <span className="text-sm font-medium text-orange-800">Tahap Saat Ini</span>
                    </div>
                    <p className="font-semibold text-gray-900">{currentStage.title}</p>
                    <p className="text-sm text-gray-600 mt-1">{currentStage.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Job Order Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-gray-800 flex items-center">
                  <Building2 className="h-5 w-5 mr-2 text-orange-600" />
                  Job Order
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="font-medium text-gray-900">{studentData.jobOrder}</p>
                <p className="text-sm text-gray-500 mt-1">
                  Terdaftar sejak{" "}
                  {new Date(studentData.tanggalDaftar).toLocaleDateString("id-ID", {
                    day: "2-digit",
                    month: "long",
                    year: "numeric",
                  })}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Timeline */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
                <CardTitle className="text-xl text-gray-800">Timeline Progress Seleksi</CardTitle>
                <CardDescription>Tracking lengkap tahapan seleksi magang ke Jepang</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="relative">
                  {timelineStages.map((stage, index) => {
                    const statusConfig = getStatusConfig(stage.status)
                    const StageIcon = stage.icon
                    const StatusIcon = statusConfig.icon
                    const isLast = index === timelineStages.length - 1

                    return (
                      <div key={stage.id} className="relative flex items-start pb-8">
                        {/* Timeline Line */}
                        {!isLast && (
                          <div
                            className={cn(
                              "absolute left-6 top-12 w-0.5 h-full",
                              stage.status === "selesai" ? "bg-maroon-300" : "bg-gray-300",
                            )}
                          />
                        )}

                        {/* Timeline Node */}
                        <div className="relative flex-shrink-0">
                          <div
                            className={cn(
                              "w-12 h-12 rounded-full flex items-center justify-center border-4 border-white shadow-lg",
                              statusConfig.color,
                            )}
                          >
                            <StageIcon className="h-5 w-5 text-white" />
                          </div>
                          {/* Status Indicator */}
                          <div className="absolute -bottom-1 -right-1">
                            <div
                              className={cn(
                                "w-6 h-6 rounded-full flex items-center justify-center border-2 border-white",
                                statusConfig.color,
                              )}
                            >
                              <StatusIcon className="h-3 w-3 text-white" />
                            </div>
                          </div>
                        </div>

                        {/* Timeline Content */}
                        <div className="ml-6 flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">{stage.title}</h3>
                            <Badge variant="outline" className={statusConfig.badge}>
                              {statusConfig.label}
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-3">{stage.description}</p>

                          {/* Date and Notes */}
                          <div className={cn("rounded-lg p-4 border", statusConfig.bgColor, statusConfig.borderColor)}>
                            {stage.tanggal && (
                              <div className="flex items-center mb-2">
                                <Calendar className={cn("h-4 w-4 mr-2", statusConfig.textColor)} />
                                <span className="text-sm font-medium text-gray-700">
                                  {new Date(stage.tanggal).toLocaleDateString("id-ID", {
                                    day: "2-digit",
                                    month: "long",
                                    year: "numeric",
                                  })}
                                </span>
                              </div>
                            )}
                            {stage.catatan ? (
                              <div className="flex items-start">
                                <FileText className={cn("h-4 w-4 mr-2 mt-0.5 flex-shrink-0", statusConfig.textColor)} />
                                <p className="text-sm text-gray-700">{stage.catatan}</p>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <Clock className={cn("h-4 w-4 mr-2", statusConfig.textColor)} />
                                <span className="text-sm text-gray-500 italic">
                                  {stage.status === "berlangsung" ? "Sedang dalam proses" : "Menunggu tahap sebelumnya"}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
