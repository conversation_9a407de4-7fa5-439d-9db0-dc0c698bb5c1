"use client"

import { useState, useMemo, useEffect } from "react"
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from "recharts"
import {
  Users,
  UserCheck,
  Plane,
  Building2,
  MapPin,
  Filter,
  TrendingUp,
  Globe,
  Calendar,
  Eye,
  AlertTriangle,
  Loader2,
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { PenempatanService, type PenempatanSiswa } from "@/lib/services"

export default function PenempatanPage() {
  const [penempatanData, setPenempatanData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [yearFilter, setYearFilter] = useState<string>(new Date().getFullYear().toString())
  const [monthlyData, setMonthlyData] = useState<any[]>([])

  // Fetch Penempatan data
  useEffect(() => {
    const fetchPenempatanData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const [placements, monthlyStats] = await Promise.all([
          PenempatanService.getAll(),
          PenempatanService.getMonthlyStats(parseInt(yearFilter))
        ])
        
        setPenempatanData(placements)
        setMonthlyData(monthlyStats)
      } catch (err) {
        console.error('Error fetching Penempatan data:', err)
        setError('Gagal memuat data Penempatan. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchPenempatanData()
  }, [yearFilter])

  // Filter data based on search term and status
  const filteredData = useMemo(() => {
    let filtered = penempatanData

    if (searchTerm) {
      filtered = filtered.filter(
        (penempatan) =>
          penempatan.siswa?.nama_lengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
          penempatan.perusahaan_penerima?.nama_perusahaan.toLowerCase().includes(searchTerm.toLowerCase()) ||
          penempatan.kumiai?.nama_kumiai.toLowerCase().includes(searchTerm.toLowerCase()) ||
          penempatan.posisi_kerja.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter && statusFilter !== "all") {
      filtered = filtered.filter((penempatan) => penempatan.status_penempatan === statusFilter)
    }

    return filtered
  }, [penempatanData, searchTerm, statusFilter])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalPenempatan = penempatanData.length
    const aktif = penempatanData.filter((p) => p.status_penempatan === "aktif").length
    const berangkat = penempatanData.filter((p) => p.status_penempatan === "berangkat").length
    const selesai = penempatanData.filter((p) => p.status_penempatan === "selesai").length
    const ditempatkan = penempatanData.filter((p) => p.status_penempatan === "ditempatkan").length

    return {
      totalPenempatan,
      aktif,
      berangkat,
      selesai,
      ditempatkan,
    }
  }, [penempatanData])

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'aktif':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Aktif</Badge>
      case 'berangkat':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Berangkat</Badge>
      case 'selesai':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Selesai</Badge>
      case 'ditempatkan':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Ditempatkan</Badge>
      case 'dibatalkan':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Dibatalkan</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Prepare chart data
  const statusChartData = [
    { name: "Aktif", value: statistics.aktif, color: "#22c55e" },
    { name: "Berangkat", value: statistics.berangkat, color: "#3b82f6" },
    { name: "Selesai", value: statistics.selesai, color: "#8b5cf6" },
    { name: "Ditempatkan", value: statistics.ditempatkan, color: "#f59e0b" },
  ].filter(item => item.value > 0)

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data Penempatan...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Penempatan Siswa</h1>
          <p className="text-gray-600 mt-1">Monitor dan kelola penempatan siswa di perusahaan Jepang</p>
        </div>
        <div className="flex gap-2">
          <Select value={yearFilter} onValueChange={setYearFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022">2022</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Penempatan</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalPenempatan}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Siswa ditempatkan</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Aktif di Jepang</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.aktif}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Sedang bekerja</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Sudah Berangkat</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <Plane className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.berangkat}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Dalam perjalanan</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Selesai Program</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.selesai}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Sudah kembali</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Placement Chart */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Trend Penempatan Bulanan {yearFilter}</CardTitle>
            <CardDescription>Jumlah penempatan siswa per bulan</CardDescription>
          </CardHeader>
          <CardContent>
            {monthlyData.length > 0 ? (
              <ChartContainer
                config={{
                  count: { label: "Penempatan", color: "#3b82f6" },
                }}
                className="h-[300px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="count" fill="var(--color-count)" radius={4} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-gray-500">
                <p>Tidak ada data penempatan untuk tahun {yearFilter}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status Distribution Chart */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Distribusi Status Penempatan</CardTitle>
            <CardDescription>Komposisi status siswa saat ini</CardDescription>
          </CardHeader>
          <CardContent>
            {statusChartData.length > 0 ? (
              <>
                <ChartContainer
                  config={{
                    aktif: { label: "Aktif", color: "#22c55e" },
                    berangkat: { label: "Berangkat", color: "#3b82f6" },
                    selesai: { label: "Selesai", color: "#8b5cf6" },
                    ditempatkan: { label: "Ditempatkan", color: "#f59e0b" },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={statusChartData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {statusChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <ChartTooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload
                            return (
                              <div className="bg-white p-3 border rounded-lg shadow-lg">
                                <p className="font-medium">{data.name}</p>
                                <p className="text-sm text-gray-600">{data.value} siswa</p>
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>

                {/* Legend */}
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {statusChartData.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }} />
                      <span className="text-xs text-gray-600">{item.name} ({item.value})</span>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-gray-500">
                <p>Tidak ada data penempatan</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar Penempatan Siswa</CardTitle>
              <CardDescription>Data penempatan siswa di perusahaan Jepang</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <div className="relative w-full sm:w-80">
                <Input
                  placeholder="Cari berdasarkan nama siswa atau perusahaan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="berangkat">Berangkat</SelectItem>
                  <SelectItem value="selesai">Selesai</SelectItem>
                  <SelectItem value="ditempatkan">Ditempatkan</SelectItem>
                  <SelectItem value="dibatalkan">Dibatalkan</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Siswa</TableHead>
                  <TableHead className="font-semibold">Perusahaan</TableHead>
                  <TableHead className="font-semibold">Posisi</TableHead>
                  <TableHead className="font-semibold">Lokasi</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Tanggal</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((penempatan, index) => (
                    <TableRow key={penempatan.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">
                            {penempatan.siswa?.nama_lengkap || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {penempatan.siswa?.lpk_mitra?.nama_lpk || 'N/A'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">
                            {penempatan.perusahaan_penerima?.nama_perusahaan || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {penempatan.kumiai?.nama_kumiai || 'N/A'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-700">{penempatan.posisi_kerja}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-700">
                            {penempatan.perusahaan_penerima?.prefektur || 'N/A'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(penempatan.status_penempatan)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-gray-400" />
                            <span>{new Date(penempatan.tanggal_penempatan).toLocaleDateString('id-ID')}</span>
                          </div>
                          {penempatan.tanggal_keberangkatan && (
                            <div className="text-xs text-gray-500 mt-1">
                              Berangkat: {new Date(penempatan.tanggal_keberangkatan).toLocaleDateString('id-ID')}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-blue-50 hover:border-blue-300"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada data Penempatan</h3>
                        <p className="text-gray-500">
                          {searchTerm || statusFilter
                            ? "Tidak ditemukan Penempatan yang sesuai dengan filter"
                            : "Belum ada Penempatan yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {penempatanData.length} Penempatan
                </span>
                {(searchTerm || statusFilter) && (
                  <span className="text-blue-600">Filter aktif</span>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
