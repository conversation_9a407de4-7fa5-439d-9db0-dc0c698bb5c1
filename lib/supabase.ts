import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
})

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error)
  
  if (error?.message) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}

// Helper function to format Supabase response
export const formatSupabaseResponse = <T>(data: T[] | null, error: any) => {
  if (error) {
    throw new Error(handleSupabaseError(error))
  }
  
  return data || []
}

// Helper function for single record response
export const formatSupabaseSingleResponse = <T>(data: T | null, error: any) => {
  if (error) {
    throw new Error(handleSupabaseError(error))
  }
  
  return data
}
