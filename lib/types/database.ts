// Database Types for Dashboard Magang Jepang
// Generated from Supabase schema

export type UserRole = 'admin' | 'operator' | 'lpk_admin' | 'viewer'
export type GenderType = 'L' | 'P'
export type EducationLevel = 'SD' | 'SMP' | 'SMA' | 'SMK' | 'D3' | 'S1'
export type StatusType = 'aktif' | 'nonaktif' | 'suspended'
export type RegistrationStatus = 'draft' | 'submitted' | 'review' | 'approved' | 'rejected'
export type PlacementStatus = 'ditempatkan' | 'berangkat' | 'aktif' | 'selesai' | 'dibatalkan'
export type JobStatus = 'draft' | 'published' | 'closed' | 'cancelled'
export type DocumentStatus = 'pending' | 'approved' | 'rejected'

// LPK Mitra
export interface LpkMitra {
  id: string
  nama_lpk: string
  alamat_lengkap: string
  kota: string
  provinsi: string
  nama_pimpinan: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  tanggal_kerjasama: string
  catatan?: string
  created_at: string
  updated_at: string
}

// Kumiai
export interface Kumiai {
  id: string
  nama_kumiai: string
  kode_kumiai: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  keterangan?: string
  created_at: string
  updated_at: string
}

// Perusahaan Penerima
export interface PerusahaanPenerima {
  id: string
  kumiai_id: string
  nama_perusahaan: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  bidang_usaha: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  keterangan?: string
  created_at: string
  updated_at: string
}

// Job Order
export interface JobOrder {
  id: string
  perusahaan_id: string
  kumiai_id: string
  judul_pekerjaan: string
  deskripsi_pekerjaan: string
  posisi: string
  bidang_kerja: string
  jenis_kelamin: string
  usia_min: number
  usia_max: number
  pendidikan_min: EducationLevel
  pengalaman_kerja?: string
  keahlian_khusus?: string
  gaji_pokok: number
  tunjangan: number
  jam_kerja_per_hari: number
  hari_kerja_per_minggu: number
  overtime_available: boolean
  akomodasi?: string
  transportasi?: string
  asuransi?: string
  fasilitas_lain?: string
  jumlah_kuota: number
  kuota_terisi: number
  status: JobStatus
  tanggal_buka: string
  tanggal_tutup: string
  created_at: string
  updated_at: string
}

// Siswa
export interface Siswa {
  id: string
  lpk_id: string
  nama_lengkap: string
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: GenderType
  agama: string
  status_pernikahan: string
  alamat_lengkap: string
  kelurahan: string
  kecamatan: string
  kota_kabupaten: string
  provinsi: string
  kode_pos?: string
  nomor_hp: string
  email?: string
  pendidikan_terakhir: EducationLevel
  nama_sekolah?: string
  tahun_lulus?: number
  jurusan?: string
  nama_ayah?: string
  nama_ibu?: string
  alamat_keluarga?: string
  nomor_hp_keluarga?: string
  status_pendaftaran: RegistrationStatus
  tanggal_daftar: string
  catatan?: string
  created_at: string
  updated_at: string
}

// Penempatan Siswa
export interface PenempatanSiswa {
  id: string
  siswa_id: string
  job_order_id: string
  perusahaan_id: string
  kumiai_id: string
  tanggal_penempatan: string
  tanggal_keberangkatan?: string
  tanggal_kepulangan?: string
  status_penempatan: PlacementStatus
  posisi_kerja: string
  gaji_aktual: number
  alamat_kerja: string
  evaluasi_bulanan?: string
  catatan_khusus?: string
  created_at: string
  updated_at: string
}

// Database Tables Type
export interface Database {
  public: {
    Tables: {
      lpk_mitra: {
        Row: LpkMitra
        Insert: Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>>
      }
      kumiai: {
        Row: Kumiai
        Insert: Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>>
      }
      perusahaan_penerima: {
        Row: PerusahaanPenerima
        Insert: Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>>
      }
      job_order: {
        Row: JobOrder
        Insert: Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa: {
        Row: Siswa
        Insert: Omit<Siswa, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Siswa, 'id' | 'created_at' | 'updated_at'>>
      }
      penempatan_siswa: {
        Row: PenempatanSiswa
        Insert: Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}
