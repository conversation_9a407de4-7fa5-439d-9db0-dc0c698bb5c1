import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { Siswa } from '@/lib/types/database'

export class SiswaService {
  // Get all Siswa with optional filtering
  static async getAll(filters?: {
    status_pendaftaran?: string
    lpk_id?: string
    jenis_kelamin?: string
    pendidikan_terakhir?: string
    kota_kabupaten?: string
    provinsi?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(nama_lpk),
        penempatan_siswa(
          id,
          status_penempatan,
          job_order(judul_pekerjaan),
          perusahaan_penerima(nama_perusahaan)
        )
      `)

    // Apply filters
    if (filters?.status_pendaftaran) {
      query = query.eq('status_pendaftaran', filters.status_pendaftaran)
    }

    if (filters?.lpk_id) {
      query = query.eq('lpk_id', filters.lpk_id)
    }

    if (filters?.jenis_kelamin) {
      query = query.eq('jenis_kelamin', filters.jenis_kelamin)
    }

    if (filters?.pendidikan_terakhir) {
      query = query.eq('pendidikan_terakhir', filters.pendidikan_terakhir)
    }

    if (filters?.kota_kabupaten) {
      query = query.eq('kota_kabupaten', filters.kota_kabupaten)
    }

    if (filters?.provinsi) {
      query = query.eq('provinsi', filters.provinsi)
    }

    if (filters?.search) {
      query = query.or(`nama_lengkap.ilike.%${filters.search}%,nik.ilike.%${filters.search}%,email.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Siswa by ID with related data
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(*),
        penempatan_siswa(
          *,
          job_order(*),
          perusahaan_penerima(*),
          kumiai(*)
        )
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Siswa
  static async create(siswaData: Omit<Siswa, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('siswa')
      .insert(siswaData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Siswa
  static async update(id: string, siswaData: Partial<Omit<Siswa, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('siswa')
      .update({
        ...siswaData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Siswa
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('siswa')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Siswa statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('siswa')
      .select('id', { count: 'exact' })

    const { data: approvedData, error: approvedError } = await supabase
      .from('siswa')
      .select('id', { count: 'exact' })
      .eq('status_pendaftaran', 'approved')

    const { data: placedData, error: placedError } = await supabase
      .from('penempatan_siswa')
      .select('siswa_id', { count: 'exact' })
      .in('status_penempatan', ['ditempatkan', 'berangkat', 'aktif'])

    if (totalError || approvedError || placedError) {
      throw new Error('Failed to fetch Siswa statistics')
    }

    return {
      total: totalData?.length || 0,
      approved: approvedData?.length || 0,
      placed: placedData?.length || 0,
      pending: (totalData?.length || 0) - (approvedData?.length || 0)
    }
  }

  // Get Siswa by LPK
  static async getByLpk(lpkId: string) {
    const { data, error } = await supabase
      .from('siswa')
      .select(`
        *,
        penempatan_siswa(
          id,
          status_penempatan,
          job_order(judul_pekerjaan),
          perusahaan_penerima(nama_perusahaan)
        )
      `)
      .eq('lpk_id', lpkId)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get available students for placement (approved but not placed)
  static async getAvailableForPlacement() {
    const { data, error } = await supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(nama_lpk)
      `)
      .eq('status_pendaftaran', 'approved')
      .not('id', 'in', 
        supabase
          .from('penempatan_siswa')
          .select('siswa_id')
          .in('status_penempatan', ['ditempatkan', 'berangkat', 'aktif'])
      )
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique cities for filter options
  static async getUniqueCities() {
    const { data, error } = await supabase
      .from('siswa')
      .select('kota_kabupaten')
      .order('kota_kabupaten')

    if (error) {
      throw new Error('Failed to fetch cities')
    }

    const uniqueCities = [...new Set(data?.map(item => item.kota_kabupaten) || [])]
    return uniqueCities
  }

  // Get unique provinces for filter options
  static async getUniqueProvinces() {
    const { data, error } = await supabase
      .from('siswa')
      .select('provinsi')
      .order('provinsi')

    if (error) {
      throw new Error('Failed to fetch provinces')
    }

    const uniqueProvinces = [...new Set(data?.map(item => item.provinsi) || [])]
    return uniqueProvinces
  }

  // Check if NIK already exists
  static async checkNikExists(nik: string, excludeId?: string) {
    let query = supabase
      .from('siswa')
      .select('id')
      .eq('nik', nik)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error('Failed to check NIK')
    }

    return (data?.length || 0) > 0
  }
}
