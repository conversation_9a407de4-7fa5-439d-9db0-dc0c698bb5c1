import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { JobOrder } from '@/lib/types/database'

export class JobOrderService {
  // Get all Job Orders with optional filtering
  static async getAll(filters?: {
    status?: string
    perusahaan_id?: string
    kumiai_id?: string
    bidang_kerja?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(nama_perusahaan, prefektur),
        kumiai(nama_kumiai, kode_kumiai)
      `)

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.perusahaan_id) {
      query = query.eq('perusahaan_id', filters.perusahaan_id)
    }

    if (filters?.kumiai_id) {
      query = query.eq('kumiai_id', filters.kumiai_id)
    }

    if (filters?.bidang_kerja) {
      query = query.eq('bidang_kerja', filters.bidang_kerja)
    }

    if (filters?.search) {
      query = query.or(`judul_pekerjaan.ilike.%${filters.search}%,posisi.ilike.%${filters.search}%,bidang_kerja.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Job Order by ID with related data
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(*),
        kumiai(*),
        penempatan_siswa(
          id,
          siswa_id,
          status_penempatan,
          siswa(nama_lengkap, lpk_id, lpk_mitra(nama_lpk))
        )
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Job Order
  static async create(jobOrderData: Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('job_order')
      .insert(jobOrderData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Job Order
  static async update(id: string, jobOrderData: Partial<Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('job_order')
      .update({
        ...jobOrderData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Job Order
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('job_order')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Job Order statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })

    const { data: publishedData, error: publishedError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })
      .eq('status', 'published')

    const { data: closedData, error: closedError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })
      .eq('status', 'closed')

    if (totalError || publishedError || closedError) {
      throw new Error('Failed to fetch Job Order statistics')
    }

    return {
      total: totalData?.length || 0,
      published: publishedData?.length || 0,
      closed: closedData?.length || 0,
      draft: (totalData?.length || 0) - (publishedData?.length || 0) - (closedData?.length || 0)
    }
  }

  // Get Job Orders with placement statistics
  static async getWithPlacementStats() {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(nama_perusahaan),
        kumiai(nama_kumiai),
        penempatan_siswa(count)
      `)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique bidang_kerja for filter options
  static async getUniqueBidangKerja() {
    const { data, error } = await supabase
      .from('job_order')
      .select('bidang_kerja')
      .order('bidang_kerja')

    if (error) {
      throw new Error('Failed to fetch bidang kerja')
    }

    const uniqueBidangKerja = [...new Set(data?.map(item => item.bidang_kerja) || [])]
    return uniqueBidangKerja
  }

  // Get active Job Orders for placement
  static async getActiveJobOrders() {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        id,
        judul_pekerjaan,
        posisi,
        jumlah_kuota,
        kuota_terisi,
        perusahaan_penerima(nama_perusahaan),
        kumiai(nama_kumiai)
      `)
      .eq('status', 'published')
      .lt('kuota_terisi', supabase.raw('jumlah_kuota'))
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Update quota when student is placed
  static async updateQuota(id: string, increment: number = 1) {
    const { data, error } = await supabase
      .from('job_order')
      .update({
        kuota_terisi: supabase.raw(`kuota_terisi + ${increment}`),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }
}
