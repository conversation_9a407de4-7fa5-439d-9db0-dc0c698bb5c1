# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Supabase
.branches
.temp

# Database
*.db
*.sqlite
*.sqlite3

# Uploads (if using local storage)
uploads/
public/uploads/

# Cache
.cache/
.parcel-cache/

# Testing
__tests__/__snapshots__/
test-results/
playwright-report/
playwright/.cache/

# Generated types (will be regenerated)
types/supabase.ts

# Lock files (keep pnpm-lock.yaml)
package-lock.json
yarn.lock

# Backup files
*.backup
*.bak
*.sql.gz
*.dump

# Security
.secrets
secrets/
*.secret

# Custom
custom/
local/
private/