'use client'

import React, { useState, useCallback } from 'react'
import { MapPin, Building2, Users, DollarSign } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// Note: Mapbox implementation will be added later when library issues are resolved

interface PlacementData {
  id: string
  siswa_id: string
  siswa?: {
    nama_lengkap: string
    jenis_kelamin: string
  }
  perusahaan_penerima?: {
    nama_perusahaan: string
    alamat_jepang: string
    kota_jepang: string
    prefektur: string
    bidang_usaha: string
  }
  kumiai?: {
    nama_kumiai: string
    kode_kumiai: string
  }
  posisi_kerja: string
  gaji_aktual: number
  status_penempatan: string
  tanggal_penempatan: string
}

interface MapboxWrapperProps {
  data: PlacementData[]
  height?: string
  mapboxToken: string
}

// Prefecture coordinates for Japan
const PREFECTURE_COORDINATES: Record<string, { lat: number; lng: number }> = {
  'Tokyo': { lat: 35.6762, lng: 139.6503 },
  'Osaka': { lat: 34.6937, lng: 135.5023 },
  'Aichi': { lat: 35.1802, lng: 136.9066 },
  'Kanagawa': { lat: 35.4478, lng: 139.6425 },
  'Saitama': { lat: 35.8617, lng: 139.6455 },
  'Chiba': { lat: 35.6074, lng: 140.1065 },
  'Hyogo': { lat: 34.6913, lng: 135.1830 },
  'Kyoto': { lat: 35.0116, lng: 135.7681 },
  'Fukuoka': { lat: 33.5904, lng: 130.4017 },
  'Hokkaido': { lat: 43.2642, lng: 142.7297 },
  'Miyagi': { lat: 38.2682, lng: 140.8694 },
  'Hiroshima': { lat: 34.3853, lng: 132.4553 },
  'Shizuoka': { lat: 34.9756, lng: 138.3828 },
  'Gunma': { lat: 36.3911, lng: 139.0608 },
  'Ibaraki': { lat: 36.3418, lng: 140.4468 },
  'Tochigi': { lat: 36.5658, lng: 139.8836 },
  'Nara': { lat: 34.6851, lng: 135.8048 },
  'Shiga': { lat: 35.0045, lng: 135.8686 },
  'Mie': { lat: 34.7303, lng: 136.5086 },
  'Wakayama': { lat: 34.2261, lng: 135.1675 }
}

export default function MapboxWrapper({ data, height = '500px', mapboxToken }: MapboxWrapperProps) {
  const [selectedMarker, setSelectedMarker] = useState<any>(null)
  const mapContainer = React.useRef<HTMLDivElement>(null)
  const map = React.useRef<any>(null)



  // Group data by prefecture for statistics
  const prefectureData = React.useMemo(() => {
    const grouped = data.reduce((acc, placement) => {
      const prefecture = placement.perusahaan_penerima?.prefektur
      if (!prefecture || !PREFECTURE_COORDINATES[prefecture]) return acc

      if (!acc[prefecture]) {
        acc[prefecture] = {
          prefecture,
          coordinates: PREFECTURE_COORDINATES[prefecture],
          placements: [],
          totalStudents: 0,
          companies: new Set(),
          averageSalary: 0
        }
      }

      acc[prefecture].placements.push(placement)
      acc[prefecture].totalStudents += 1
      if (placement.perusahaan_penerima?.nama_perusahaan) {
        acc[prefecture].companies.add(placement.perusahaan_penerima.nama_perusahaan)
      }

      return acc
    }, {} as Record<string, any>)

    // Calculate average salary for each prefecture
    Object.values(grouped).forEach((prefData: any) => {
      const totalSalary = prefData.placements.reduce((sum: number, p: PlacementData) =>
        sum + (p.gaji_aktual || 0), 0)
      prefData.averageSalary = Math.round(totalSalary / prefData.totalStudents)
      prefData.companiesCount = prefData.companies.size
    })

    return Object.values(grouped).sort((a: any, b: any) => b.totalStudents - a.totalStudents)
  }, [data])

  // Get marker color based on student count
  const getMarkerColor = (studentCount: number) => {
    if (studentCount >= 20) return '#800000' // Maroon for high concentration
    if (studentCount >= 10) return '#FFA500' // Orange for medium concentration
    if (studentCount >= 5) return '#FFD700'  // Gold for low-medium concentration
    return '#87CEEB' // Sky blue for low concentration
  }

  // Get marker size based on student count
  const getMarkerSize = (studentCount: number) => {
    if (studentCount >= 20) return 40
    if (studentCount >= 10) return 32
    if (studentCount >= 5) return 24
    return 16
  }

  const handleMarkerClick = useCallback((prefData: any) => {
    setSelectedMarker(prefData)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'aktif':
        return 'default'
      case 'berangkat':
        return 'secondary'
      case 'selesai':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  // For now, show an enhanced prefecture visualization instead of the problematic map
  return (
    <div className="w-full" style={{ height }}>
      <div className="h-full bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 overflow-y-auto">
        <div className="text-center mb-6">
          <MapPin className="h-12 w-12 text-purple-600 mx-auto mb-3" />
          <h3 className="text-xl font-bold text-purple-800 mb-2">
            Distribusi Geografis Siswa di Jepang
          </h3>
          <p className="text-purple-600 text-sm">
            Visualisasi penempatan siswa berdasarkan prefecture dan perusahaan penerima
          </p>
        </div>

        {/* Prefecture Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {prefectureData.slice(0, 12).map((prefData, index) => (
            <Card
              key={prefData.prefecture}
              className="hover:shadow-lg transition-shadow cursor-pointer border-l-4"
              style={{ borderLeftColor: getMarkerColor(prefData.totalStudents) }}
              onClick={() => setSelectedMarker(selectedMarker?.prefecture === prefData.prefecture ? null : prefData)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-bold text-lg text-gray-900">{prefData.prefecture}</h4>
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold"
                    style={{ backgroundColor: getMarkerColor(prefData.totalStudents) }}
                  >
                    {prefData.totalStudents}
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="text-gray-600">Siswa</span>
                    </div>
                    <span className="font-medium">{prefData.totalStudents}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4 text-green-600" />
                      <span className="text-gray-600">Perusahaan</span>
                    </div>
                    <span className="font-medium">{prefData.companiesCount}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-yellow-600" />
                      <span className="text-gray-600">Rata-rata Gaji</span>
                    </div>
                    <span className="font-medium text-xs">{formatCurrency(prefData.averageSalary)}</span>
                  </div>
                </div>

                {/* Ranking Badge */}
                <div className="mt-3 flex justify-between items-center">
                  <Badge variant="secondary" className="text-xs">
                    Ranking #{index + 1}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {prefData.placements.length} penempatan
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Selected Prefecture Details */}
        {selectedMarker && (
          <Card className="mt-6 border-2 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-purple-800 flex items-center">
                  <MapPin className="h-6 w-6 mr-2" />
                  Detail {selectedMarker.prefecture}
                </h3>
                <button
                  onClick={() => setSelectedMarker(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>

              {/* Statistics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-800">{selectedMarker.totalStudents}</div>
                  <div className="text-sm text-blue-600">Total Siswa</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <Building2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-800">{selectedMarker.companiesCount}</div>
                  <div className="text-sm text-green-600">Perusahaan</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg text-center">
                  <DollarSign className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                  <div className="text-lg font-bold text-yellow-800">{formatCurrency(selectedMarker.averageSalary)}</div>
                  <div className="text-sm text-yellow-600">Rata-rata Gaji</div>
                </div>
              </div>

              {/* Recent Placements */}
              <div>
                <h4 className="font-bold text-lg text-gray-900 mb-3">Penempatan Terbaru</h4>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {selectedMarker.placements.slice(0, 6).map((placement: PlacementData) => (
                    <div key={placement.id} className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{placement.siswa?.nama_lengkap}</div>
                          <div className="text-sm text-gray-600">{placement.perusahaan_penerima?.nama_perusahaan}</div>
                          <div className="text-sm text-gray-500">{placement.posisi_kerja}</div>
                        </div>
                        <Badge
                          variant={getStatusBadgeVariant(placement.status_penempatan)}
                          className="text-xs ml-2"
                        >
                          {placement.status_penempatan}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {selectedMarker.placements.length > 6 && (
                    <div className="text-center text-sm text-gray-500 py-2">
                      +{selectedMarker.placements.length - 6} penempatan lainnya
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Legend */}
        <div className="mt-6 bg-white p-4 rounded-lg shadow-sm border">
          <h4 className="font-medium text-sm text-gray-900 mb-3">Keterangan Warna</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded-full bg-sky-400"></div>
              <span>1-4 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#FFD700' }}></div>
              <span>5-9 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded-full bg-orange-500"></div>
              <span>10-19 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded-full bg-red-900"></div>
              <span>20+ siswa</span>
            </div>
          </div>
        </div>

        {/* Note about map */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            💡 Peta interaktif Mapbox akan tersedia setelah konfigurasi library selesai
          </p>
        </div>
      </div>
    </div>
  )
}
