'use client'

import React, { useState, useCallback } from 'react'
import { MapPin, Building2, Users, Yen } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// Import Mapbox components
let Map: any, Marker: any, Popup: any, NavigationControl: any, FullscreenControl: any, ScaleControl: any

if (typeof window !== 'undefined') {
  try {
    const mapboxgl = require('react-map-gl')
    Map = mapboxgl.default || mapboxgl.Map
    Marker = mapboxgl.Marker
    Popup = mapboxgl.Popup
    NavigationControl = mapboxgl.NavigationControl
    FullscreenControl = mapboxgl.FullscreenControl
    ScaleControl = mapboxgl.ScaleControl
    
    // Import CSS
    require('mapbox-gl/dist/mapbox-gl.css')
  } catch (error) {
    console.warn('Mapbox GL JS not available:', error)
  }
}

interface PlacementData {
  id: string
  siswa_id: string
  siswa?: {
    nama_lengkap: string
    jenis_kelamin: string
  }
  perusahaan_penerima?: {
    nama_perusahaan: string
    alamat_jepang: string
    kota_jepang: string
    prefektur: string
    bidang_usaha: string
  }
  kumiai?: {
    nama_kumiai: string
    kode_kumiai: string
  }
  posisi_kerja: string
  gaji_aktual: number
  status_penempatan: string
  tanggal_penempatan: string
}

interface MapboxWrapperProps {
  data: PlacementData[]
  height?: string
  mapboxToken: string
}

// Prefecture coordinates for Japan
const PREFECTURE_COORDINATES: Record<string, { lat: number; lng: number }> = {
  'Tokyo': { lat: 35.6762, lng: 139.6503 },
  'Osaka': { lat: 34.6937, lng: 135.5023 },
  'Aichi': { lat: 35.1802, lng: 136.9066 },
  'Kanagawa': { lat: 35.4478, lng: 139.6425 },
  'Saitama': { lat: 35.8617, lng: 139.6455 },
  'Chiba': { lat: 35.6074, lng: 140.1065 },
  'Hyogo': { lat: 34.6913, lng: 135.1830 },
  'Kyoto': { lat: 35.0116, lng: 135.7681 },
  'Fukuoka': { lat: 33.5904, lng: 130.4017 },
  'Hokkaido': { lat: 43.2642, lng: 142.7297 },
  'Miyagi': { lat: 38.2682, lng: 140.8694 },
  'Hiroshima': { lat: 34.3853, lng: 132.4553 },
  'Shizuoka': { lat: 34.9756, lng: 138.3828 },
  'Gunma': { lat: 36.3911, lng: 139.0608 },
  'Ibaraki': { lat: 36.3418, lng: 140.4468 },
  'Tochigi': { lat: 36.5658, lng: 139.8836 },
  'Nara': { lat: 34.6851, lng: 135.8048 },
  'Shiga': { lat: 35.0045, lng: 135.8686 },
  'Mie': { lat: 34.7303, lng: 136.5086 },
  'Wakayama': { lat: 34.2261, lng: 135.1675 }
}

export default function MapboxWrapper({ data, height = '500px', mapboxToken }: MapboxWrapperProps) {
  const [selectedMarker, setSelectedMarker] = useState<any>(null)
  const [viewState, setViewState] = useState({
    longitude: 138.2529,
    latitude: 36.2048,
    zoom: 5.5
  })

  // If Mapbox is not available, show fallback
  if (!Map || !mapboxToken) {
    return (
      <div className="h-full w-full bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-200">
        <div className="text-center max-w-md">
          <MapPin className="h-16 w-16 text-purple-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-purple-600 mb-2">
            {!mapboxToken ? 'Mapbox Token Required' : 'Map Loading...'}
          </h3>
          <p className="text-purple-500 text-sm mb-4">
            {!mapboxToken 
              ? 'Untuk menampilkan peta interaktif, silakan tambahkan NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN ke file .env.local'
              : 'Sedang memuat komponen peta...'
            }
          </p>
          {!mapboxToken && (
            <p className="text-xs text-gray-500">
              Dapatkan token gratis di{" "}
              <a 
                href="https://www.mapbox.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-purple-600 hover:underline"
              >
                mapbox.com
              </a>
            </p>
          )}
        </div>
      </div>
    )
  }

  // Group data by prefecture
  const prefectureData = React.useMemo(() => {
    const grouped = data.reduce((acc, placement) => {
      const prefecture = placement.perusahaan_penerima?.prefektur
      if (!prefecture || !PREFECTURE_COORDINATES[prefecture]) return acc

      if (!acc[prefecture]) {
        acc[prefecture] = {
          prefecture,
          coordinates: PREFECTURE_COORDINATES[prefecture],
          placements: [],
          totalStudents: 0,
          companies: new Set(),
          averageSalary: 0
        }
      }

      acc[prefecture].placements.push(placement)
      acc[prefecture].totalStudents += 1
      if (placement.perusahaan_penerima?.nama_perusahaan) {
        acc[prefecture].companies.add(placement.perusahaan_penerima.nama_perusahaan)
      }

      return acc
    }, {} as Record<string, any>)

    // Calculate average salary for each prefecture
    Object.values(grouped).forEach((prefData: any) => {
      const totalSalary = prefData.placements.reduce((sum: number, p: PlacementData) => 
        sum + (p.gaji_aktual || 0), 0)
      prefData.averageSalary = Math.round(totalSalary / prefData.totalStudents)
      prefData.companiesCount = prefData.companies.size
    })

    return Object.values(grouped)
  }, [data])

  // Get marker color based on student count
  const getMarkerColor = (studentCount: number) => {
    if (studentCount >= 20) return '#800000' // Maroon for high concentration
    if (studentCount >= 10) return '#FFA500' // Orange for medium concentration
    if (studentCount >= 5) return '#FFD700'  // Gold for low-medium concentration
    return '#87CEEB' // Sky blue for low concentration
  }

  // Get marker size based on student count
  const getMarkerSize = (studentCount: number) => {
    if (studentCount >= 20) return 40
    if (studentCount >= 10) return 32
    if (studentCount >= 5) return 24
    return 16
  }

  const handleMarkerClick = useCallback((prefData: any) => {
    setSelectedMarker(prefData)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'aktif':
        return 'default'
      case 'berangkat':
        return 'secondary'
      case 'selesai':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  try {
    return (
      <div className="w-full relative" style={{ height }}>
        <Map
          {...viewState}
          onMove={(evt: any) => setViewState(evt.viewState)}
          mapboxAccessToken={mapboxToken}
          style={{ width: '100%', height: '100%' }}
          mapStyle="mapbox://styles/mapbox/light-v11"
          attributionControl={false}
        >
          {/* Navigation Controls */}
          {NavigationControl && <NavigationControl position="top-right" />}
          {FullscreenControl && <FullscreenControl position="top-right" />}
          {ScaleControl && <ScaleControl position="bottom-left" />}

          {/* Prefecture Markers */}
          {Marker && prefectureData.map((prefData) => (
            <Marker
              key={prefData.prefecture}
              longitude={prefData.coordinates.lng}
              latitude={prefData.coordinates.lat}
              anchor="center"
            >
              <div
                className="cursor-pointer transform transition-transform hover:scale-110"
                onClick={() => handleMarkerClick(prefData)}
              >
                <div
                  className="rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white font-bold text-xs"
                  style={{
                    backgroundColor: getMarkerColor(prefData.totalStudents),
                    width: getMarkerSize(prefData.totalStudents),
                    height: getMarkerSize(prefData.totalStudents)
                  }}
                >
                  {prefData.totalStudents}
                </div>
              </div>
            </Marker>
          ))}

          {/* Popup for selected marker */}
          {Popup && selectedMarker && (
            <Popup
              longitude={selectedMarker.coordinates.lng}
              latitude={selectedMarker.coordinates.lat}
              anchor="bottom"
              onClose={() => setSelectedMarker(null)}
              closeButton={true}
              closeOnClick={false}
              className="max-w-sm"
            >
              <Card className="border-0 shadow-none">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Header */}
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-orange-600" />
                      <h3 className="font-semibold text-lg text-gray-900">
                        {selectedMarker.prefecture}
                      </h3>
                    </div>

                    {/* Statistics */}
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <div>
                          <div className="font-medium">{selectedMarker.totalStudents}</div>
                          <div className="text-gray-500 text-xs">Siswa</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4 text-green-600" />
                        <div>
                          <div className="font-medium">{selectedMarker.companiesCount}</div>
                          <div className="text-gray-500 text-xs">Perusahaan</div>
                        </div>
                      </div>
                    </div>

                    {/* Average Salary */}
                    <div className="flex items-center space-x-2">
                      <Yen className="h-4 w-4 text-yellow-600" />
                      <div>
                        <div className="font-medium">{formatCurrency(selectedMarker.averageSalary)}</div>
                        <div className="text-gray-500 text-xs">Rata-rata Gaji</div>
                      </div>
                    </div>

                    {/* Recent Placements */}
                    <div className="border-t pt-3">
                      <h4 className="font-medium text-sm text-gray-900 mb-2">
                        Penempatan Terbaru:
                      </h4>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {selectedMarker.placements.slice(0, 3).map((placement: PlacementData) => (
                          <div key={placement.id} className="text-xs bg-gray-50 p-2 rounded">
                            <div className="font-medium text-gray-900">
                              {placement.siswa?.nama_lengkap}
                            </div>
                            <div className="text-gray-600">
                              {placement.perusahaan_penerima?.nama_perusahaan}
                            </div>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-gray-500">{placement.posisi_kerja}</span>
                              <Badge 
                                variant={getStatusBadgeVariant(placement.status_penempatan)}
                                className="text-xs"
                              >
                                {placement.status_penempatan}
                              </Badge>
                            </div>
                          </div>
                        ))}
                        {selectedMarker.placements.length > 3 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{selectedMarker.placements.length - 3} lainnya
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Popup>
          )}
        </Map>

        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border">
          <h4 className="font-medium text-sm text-gray-900 mb-2">Jumlah Siswa</h4>
          <div className="space-y-1 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded-full bg-sky-400"></div>
              <span>1-4 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 rounded-full" style={{ backgroundColor: '#FFD700' }}></div>
              <span>5-9 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-orange-500"></div>
              <span>10-19 siswa</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-7 h-7 rounded-full bg-red-900"></div>
              <span>20+ siswa</span>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Mapbox rendering error:', error)
    return (
      <div className="h-full w-full bg-red-50 rounded-lg flex items-center justify-center border-2 border-dashed border-red-200">
        <div className="text-center max-w-md">
          <MapPin className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">Map Error</h3>
          <p className="text-red-500 text-sm">
            Terjadi kesalahan saat memuat peta. Silakan refresh halaman.
          </p>
        </div>
      </div>
    )
  }
}
